import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/audio_provider.dart';
import '../../models/quran_models.dart';
import '../../services/audio_service.dart';
import '../../widgets/audio_player_bar.dart';

class SurahReadingScreen extends StatefulWidget {
  final Surah surah;

  const SurahReadingScreen({
    super.key,
    required this.surah,
  });

  @override
  State<SurahReadingScreen> createState() => _SurahReadingScreenState();
}

class _SurahReadingScreenState extends State<SurahReadingScreen> {
  final ScrollController _scrollController = ScrollController();
  double _fontSize = 18.0;
  bool _showTranslation = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.surah.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.text_fields),
            onPressed: _showFontSizeDialog,
          ),
          IconButton(
            icon: Icon(_showTranslation ? Icons.translate : Icons.translate_outlined),
            onPressed: () {
              setState(() {
                _showTranslation = !_showTranslation;
              });
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'play_all',
                child: Row(
                  children: [
                    Icon(Icons.play_arrow),
                    SizedBox(width: 8),
                    Text('تشغيل السورة كاملة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'bookmark_surah',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_add),
                    SizedBox(width: 8),
                    Text('إضافة علامة مرجعية للسورة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share_surah',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة السورة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Surah header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: Column(
              children: [
                Text(
                  widget.surah.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Amiri',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.surah.englishNameTranslation,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.surah.numberOfAyahs} آية • ${widget.surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية'}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 12),

                // Play button for the whole surah
                ElevatedButton.icon(
                  onPressed: _playFullSurah,
                  icon: const Icon(Icons.play_arrow, color: Colors.white),
                  label: const Text(
                    'تشغيل السورة كاملة',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Ayahs list
          Expanded(
            child: Consumer<QuranProvider>(
              builder: (context, quranProvider, child) {
                if (widget.surah.ayahs.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('جاري تحميل آيات السورة...'),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: widget.surah.ayahs.length,
                  itemBuilder: (context, index) {
                    final ayah = widget.surah.ayahs[index];
                    return _buildAyahCard(ayah, quranProvider);
                  },
                );
              },
            ),
          ),

          // Audio player bar
          const AudioPlayerBar(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        },
        child: const Icon(Icons.keyboard_arrow_up),
      ),
    );
  }

  Widget _buildAyahCard(Ayah ayah, QuranProvider quranProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Ayah number and info
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${ayah.numberInSurah}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'الآية ${ayah.numberInSurah}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  'الجزء ${ayah.juz} - الصفحة ${ayah.page}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Ayah text
            Text(
              ayah.text,
              style: TextStyle(
                fontSize: _fontSize,
                height: 1.8,
                fontFamily: 'Amiri',
              ),
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.justify,
            ),

            // Translation (if enabled)
            if (_showTranslation) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'الترجمة غير متوفرة حالياً', // Placeholder for translation
                  style: TextStyle(
                    fontSize: _fontSize - 2,
                    color: Colors.grey[700],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],

            const SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                FutureBuilder<bool>(
                  future: quranProvider.isBookmarked(
                    widget.surah.number,
                    ayah.numberInSurah,
                  ),
                  builder: (context, snapshot) {
                    final isBookmarked = snapshot.data ?? false;
                    return IconButton(
                      icon: Icon(
                        isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                        color: isBookmarked ? Theme.of(context).primaryColor : null,
                      ),
                      onPressed: () async {
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        if (isBookmarked) {
                          await quranProvider.removeBookmark(
                            widget.surah.number,
                            ayah.numberInSurah,
                          );
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text('تم إزالة العلامة المرجعية'),
                              ),
                            );
                          }
                        } else {
                          await quranProvider.addBookmark(
                            widget.surah.number,
                            ayah.numberInSurah,
                          );
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text('تم إضافة العلامة المرجعية'),
                              ),
                            );
                          }
                        }
                        setState(() {}); // Refresh to update bookmark icon
                      },
                      tooltip: isBookmarked ? 'إزالة العلامة المرجعية' : 'إضافة علامة مرجعية',
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: () {
                    // TODO: Implement share functionality
                  },
                  tooltip: 'مشاركة الآية',
                ),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () {
                    // TODO: Implement copy to clipboard
                  },
                  tooltip: 'نسخ الآية',
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () => _showAyahOptions(ayah),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'حجم الخط: ${_fontSize.toInt()}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Slider(
                value: _fontSize,
                min: 12.0,
                max: 32.0,
                divisions: 20,
                onChanged: (value) {
                  setDialogState(() {
                    _fontSize = value;
                  });
                  setState(() {});
                },
              ),
              Text(
                'نص تجريبي بالحجم المحدد',
                style: TextStyle(fontSize: _fontSize),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'play_all':
        _playFullSurah();
        break;
      case 'bookmark_surah':
        // Bookmark the first ayah as a representative
        final quranProvider = Provider.of<QuranProvider>(context, listen: false);
        quranProvider.addBookmark(widget.surah.number, 1);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إضافة علامة مرجعية للسورة')),
        );
        break;
      case 'share_surah':
        // TODO: Implement share functionality
        break;
    }
  }

  void _showAyahOptions(Ayah ayah) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الآية'),
              onTap: () {
                Navigator.pop(context);
                _showAyahInfo(ayah);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAyahInfo(Ayah ayah) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('معلومات الآية ${ayah.numberInSurah}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('رقم الآية في السورة', '${ayah.numberInSurah}'),
            _buildInfoRow('رقم الآية في المصحف', '${ayah.number}'),
            _buildInfoRow('الجزء', '${ayah.juz}'),
            _buildInfoRow('الصفحة', '${ayah.page}'),
            _buildInfoRow('الركوع', '${ayah.ruku}'),
            _buildInfoRow('المنزل', '${ayah.manzil}'),
            _buildInfoRow('ربع الحزب', '${ayah.hizbQuarter}'),
            if (ayah.sajda) _buildInfoRow('سجدة', 'نعم'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(value),
        ],
      ),
    );
  }

  // Play full surah with saved reciter
  void _playFullSurah() async {
    try {
      print('🎵 UI: Play button pressed for ${widget.surah.name}');

      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);

      // Get the saved reciter
      final reciters = AudioService.getDefaultReciters();
      final selectedReciter = reciters.firstWhere(
        (r) => r.id == settingsProvider.selectedReciterId,
        orElse: () => reciters.first,
      );

      print('🎵 UI: Selected reciter: ${selectedReciter.arabicName}');

      // Initialize audio provider with all surahs
      final allSurahs = quranProvider.surahs;
      print('🎵 UI: Total surahs available: ${allSurahs.length}');
      audioProvider.initializeSurahs(allSurahs);

      // Show loading message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('جاري تشغيل سورة ${widget.surah.name} بصوت ${selectedReciter.arabicName}...'),
          duration: const Duration(seconds: 2),
        ),
      );

      // Start playback
      print('🎵 UI: Starting playback...');
      await audioProvider.playSurah(widget.surah, selectedReciter);
      print('🎵 UI: Playback started successfully');

    } catch (e) {
      print('❌ UI: Error in _playFullSurah: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تشغيل السورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

}