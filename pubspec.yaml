name: quraan
description: "تطبيق قرآني شامل للقرآن الكريم والأذكار والأحاديث النبوية مع أوقات الصلاة - Islamic app with <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> times"
publish_to: 'none'

# Version for Google Play Store
version: 1.0.0+1

# App metadata for better store listing
homepage: https://github.com/islamicapp/quraan
repository: https://github.com/islamicapp/quraan

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.2

  # Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.3+1
  path: ^1.9.0

  # Audio
  audioplayers: ^6.0.0
  just_audio: ^0.10.3

  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.3+1
  connectivity_plus: ^6.0.5

  # Local Notifications
  flutter_local_notifications: ^19.2.1

  # Permissions
  permission_handler: ^12.0.0+1

  # UI Components
  flutter_staggered_animations: ^1.1.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # Utilities
  shared_preferences: ^2.4.0
  intl: ^0.19.0
  url_launcher: ^6.3.0
  crypto: ^3.0.3

  # Fonts & Icons
  google_fonts: ^6.2.1
  path_provider: ^2.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.9
  flutter_launcher_icons: ^0.14.3

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icon/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/app_icon.png"

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/audio/
    - assets/data/
    - assets/icons/
    - assets/icon/
    - assets/lottie/

  # Arabic Fonts - will be added when font files are available
  # fonts:
  #   - family: Tajawal
  #     fonts:
  #       - asset: assets/fonts/Tajawal-Regular.ttf
  #       - asset: assets/fonts/Tajawal-Bold.ttf
  #         weight: 700
