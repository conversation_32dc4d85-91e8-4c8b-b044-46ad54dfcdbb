import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';
import '../../utils/rtl_helper.dart';
import '../settings/reading_settings_screen.dart';

class EnhancedMushafReader extends StatefulWidget {
  final int? initialPage;
  final int? initialSurah;

  const EnhancedMushafReader({
    super.key,
    this.initialPage,
    this.initialSurah,
  });

  @override
  State<EnhancedMushafReader> createState() => _EnhancedMushafReaderState();
}

class _EnhancedMushafReaderState extends State<EnhancedMushafReader>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  int _currentPage = 1;
  bool _showControls = true;
  bool _isFullScreen = false;
  final int _totalPages = 604; // عدد صفحات المصحف

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage ?? 1;
    _pageController = PageController(initialPage: _currentPage - 1);

    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));

    _controlsAnimationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, SettingsProvider>(
      builder: (context, quranProvider, settings, child) {
        return Scaffold(
          backgroundColor: _getBackgroundColor(settings),
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: Stack(
              children: [
                // صفحات المصحف
                _buildMushafPages(settings),

                // أشرطة التحكم
                if (_showControls && !_isFullScreen) ...[
                  _buildTopBar(context, settings),
                  _buildBottomBar(context, settings),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMushafPages(SettingsProvider settings) {
    return GestureDetector(
      onTap: _toggleControls,
      child: InteractiveViewer(
        minScale: 0.8,
        maxScale: 3.0,
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: (page) {
            setState(() {
              _currentPage = page + 1;
            });
          },
          itemCount: _totalPages,
          itemBuilder: (context, index) {
            return _buildMushafPage(index + 1, settings);
          },
        ),
      ),
    );
  }

  Widget _buildMushafPage(int pageNumber, SettingsProvider settings) {
    return Container(
      margin: EdgeInsets.all(_isFullScreen ? 0 : 16),
      decoration: BoxDecoration(
        color: _getPageColor(settings),
        borderRadius: _isFullScreen ? null : BorderRadius.circular(16),
        boxShadow: _isFullScreen ? null : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(16),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_isFullScreen ? 8 : 16),
          child: Column(
            children: [
              // رأس الصفحة
              _buildPageHeader(pageNumber, settings),

              const SizedBox(height: 16),

              // محتوى الصفحة
              _buildPageContent(pageNumber, settings),

              const SizedBox(height: 16),

              // تذييل الصفحة
              _buildPageFooter(pageNumber, settings),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageHeader(int pageNumber, SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: RTLHelper.buildRTLRow(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // معلومات السورة
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RTLHelper.buildDirectionalText(
                _getSurahName(pageNumber),
                style: TextStyle(
                  fontFamily: settings.getFontFamilyName(),
                  fontSize: settings.fontSize,
                  fontWeight: FontWeight.bold,
                  color: settings.getReadingAccentColor(),
                ),
                forceRTL: true,
              ),
              Text(
                'الجزء ${_getJuzNumber(pageNumber)}',
                style: TextStyle(
                  fontFamily: settings.getFontFamilyName(),
                  fontSize: settings.fontSize - 2,
                  color: settings.getReadingAccentColor().withValues(alpha: 0.7),
                ),
              ),
            ],
          ),

          // رقم الصفحة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '$pageNumber',
              style: TextStyle(
                color: Colors.white,
                fontSize: settings.fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageContent(int pageNumber, SettingsProvider settings) {
    // محاكاة محتوى الصفحة
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getPageColor(settings),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // البسملة (إذا كانت بداية سورة جديدة)
          if (_isNewSurahStart(pageNumber))
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: RTLHelper.buildDirectionalText(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                style: TextStyle(
                  fontFamily: settings.getFontFamilyName(),
                  fontSize: settings.fontSize + 4,
                  fontWeight: FontWeight.w600,
                  color: settings.getReadingAccentColor(),
                  height: settings.lineHeight,
                ),
                forceRTL: true,
                textAlign: TextAlign.center,
              ),
            ),

          // آيات الصفحة
          ..._generatePageAyahs(pageNumber, settings),
        ],
      ),
    );
  }

  Widget _buildPageFooter(int pageNumber, SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: RTLHelper.buildRTLRow(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // معلومات إضافية
          Text(
            'صفحة $pageNumber من $_totalPages',
            style: TextStyle(
              fontSize: settings.fontSize - 4,
              color: Colors.grey[600],
            ),
          ),

          // أيقونة المصحف
          Icon(
            Icons.auto_stories_rounded,
            size: 16,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar(BuildContext context, SettingsProvider settings) {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _controlsAnimation.value)),
            child: Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 8,
                left: 16,
                right: 16,
                bottom: 8,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.surface,
                    Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                    Colors.transparent,
                  ],
                ),
              ),
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // زر الرجوع
                  IconButton(
                    icon: RTLHelper.buildRTLIcon(Icons.arrow_back),
                    onPressed: () => Navigator.pop(context),
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      foregroundColor: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),

                  // عنوان الصفحة
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: RTLHelper.buildDirectionalText(
                      'المصحف الشريف',
                      style: TextStyle(
                        fontSize: settings.fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                      forceRTL: true,
                    ),
                  ),

                  // أزرار الإجراءات
                  RTLHelper.buildRTLRow(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.settings),
                        onPressed: () => _openReadingSettings(context),
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surface,
                          foregroundColor: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
                        onPressed: _toggleFullScreen,
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surface,
                          foregroundColor: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.bookmark_border),
                        onPressed: _addBookmark,
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surface,
                          foregroundColor: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomBar(BuildContext context, SettingsProvider settings) {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - _controlsAnimation.value)),
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // الصفحة السابقة
                  IconButton(
                    icon: RTLHelper.buildRTLIcon(Icons.chevron_left),
                    onPressed: _currentPage > 1 ? _previousPage : null,
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      foregroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),

                  // مؤشر الصفحة
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'صفحة $_currentPage من $_totalPages',
                          style: TextStyle(
                            fontSize: settings.fontSize - 2,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: _currentPage / _totalPages,
                          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // الصفحة التالية
                  IconButton(
                    icon: RTLHelper.buildRTLIcon(Icons.chevron_right),
                    onPressed: _currentPage < _totalPages ? _nextPage : null,
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      foregroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _generatePageAyahs(int pageNumber, SettingsProvider settings) {
    // محاكاة آيات الصفحة
    final ayahs = <Widget>[];
    for (int i = 1; i <= 15; i++) {
      ayahs.add(
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: RTLHelper.buildDirectionalText(
            'هذا نص تجريبي لآية رقم $i في الصفحة $pageNumber من المصحف الشريف. النص هنا للتوضيح فقط وسيتم استبداله بالنص الحقيقي للقرآن الكريم.',
            style: TextStyle(
              fontFamily: settings.getFontFamilyName(),
              fontSize: settings.fontSize + 2,
              height: settings.lineHeight,
              color: settings.getReadingTextColor(),
            ),
            forceRTL: true,
          ),
        ),
      );
    }
    return ayahs;
  }

  Color _getBackgroundColor(SettingsProvider settings) {
    return settings.getReadingBackgroundColor();
  }

  Color _getPageColor(SettingsProvider settings) {
    return settings.getReadingBackgroundColor();
  }

  String _getSurahName(int pageNumber) {
    // محاكاة أسماء السور حسب الصفحة
    if (pageNumber == 1) return 'الفاتحة';
    if (pageNumber <= 49) return 'البقرة';
    if (pageNumber <= 76) return 'آل عمران';
    if (pageNumber <= 106) return 'النساء';
    return 'سورة تجريبية';
  }

  int _getJuzNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  bool _isNewSurahStart(int pageNumber) {
    // محاكاة بداية السور
    return pageNumber == 1 || pageNumber == 2 || pageNumber == 50;
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _controlsAnimationController.forward();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _addBookmark() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة الصفحة $_currentPage للمفضلة'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _openReadingSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ReadingSettingsScreen(),
      ),
    );
  }
}
