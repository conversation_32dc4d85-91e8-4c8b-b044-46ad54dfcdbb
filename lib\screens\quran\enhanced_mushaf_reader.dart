import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart'; // افترض وجود هذا الملف
import '../../providers/settings_provider.dart'; // افترض وجود هذا الملف
import '../../models/quran_models.dart'; // افترض وجود هذا الملف لنماذج البيانات مثل Ayah
import '../../utils/rtl_helper.dart'; // افترض وجود هذا الملف
// import '../settings/reading_settings_screen.dart'; // إذا كانت لديك شاشة إعدادات خاصة بالقراءة
import 'quran_text_view.dart'; // الويدجت الجديد لعرض نص القرآن

class EnhancedMushafReader extends StatefulWidget {
  final int? initialPage;

  const EnhancedMushafReader({
    super.key,
    this.initialPage,
  });

  @override
  State<EnhancedMushafReader> createState() => _EnhancedMushafReaderState();
}

class _EnhancedMushafReaderState extends State<EnhancedMushafReader>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  int _currentPage = 1;
  bool _showControls = true;
  bool _isFullScreen = false;
  // إجمالي عدد صفحات المصحف (عادة 604)
  // يمكن جلب هذا الرقم من QuranProvider إذا كان متوفرًا أو متغيرًا
  final int _totalPages = 604; 
  Map<int, List<Ayah>> _pageCache = {}; // كاش لتخزين آيات الصفحات المحملة
  bool _isLoadingPage = false;


  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage ?? 1;
    _pageController = PageController(initialPage: _currentPage - 1);

    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(
      begin: 1.0, // ابدأ ظاهرًا
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    // لا تقم بتشغيل الأنيميشن للإخفاء مباشرة، بل عند الضغط

    // تحميل الصفحة الأولى
    _loadPageData(_currentPage);

    // حفظ الصفحة الحالية عند تغييرها
    _pageController.addListener(() {
      final newPage = _pageController.page?.round();
      if (newPage != null && newPage + 1 != _currentPage) {
         Provider.of<QuranProvider>(context, listen: false).setCurrentMushafPage(newPage + 1);
      }
    });
  }

  Future<void> _loadPageData(int pageNumber) async {
    if (_pageCache.containsKey(pageNumber) || _isLoadingPage) return;

    setState(() {
      _isLoadingPage = true;
    });

    try {
      // QuranProvider يجب أن يوفر دالة لجلب الآيات بناءً على رقم الصفحة
      final ayahsForPage = await Provider.of<QuranProvider>(context, listen: false)
          .getAyahsForPage(pageNumber);
      
      if (mounted) {
        setState(() {
          _pageCache[pageNumber] = ayahsForPage;
          _isLoadingPage = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPage = false;
        });
        // يمكنك عرض رسالة خطأ هنا
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل بيانات الصفحة $pageNumber: $e')),
        );
      }
    }
  }


  @override
  void dispose() {
    _pageController.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, SettingsProvider>(
      builder: (context, quranProvider, settings, child) {
        return Scaffold(
          backgroundColor: _getBackgroundColor(settings),
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: Stack(
              children: [
                // صفحات المصحف
                _buildMushafPages(settings, quranProvider),

                // أشرطة التحكم
                if (!_isFullScreen) ...[ // عرض فقط إذا لم يكن ملء الشاشة
                  _buildTopBar(context, settings, quranProvider),
                  _buildBottomBar(context, settings, quranProvider),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMushafPages(SettingsProvider settings, QuranProvider quranProvider) {
    return GestureDetector(
      onTap: _toggleControls,
      child: PageView.builder(
        controller: _pageController,
        reverse: true, // لعرض الصفحات من اليمين لليسار (مهم للمصحف)
        onPageChanged: (pageIndex) {
          setState(() {
            _currentPage = pageIndex + 1;
            quranProvider.setCurrentMushafPage(_currentPage); // تحديث الصفحة الحالية في المزود
          });
          _loadPageData(_currentPage); // تحميل بيانات الصفحة الجديدة
        },
        itemCount: _totalPages,
        itemBuilder: (context, index) {
          final pageNumber = index + 1;
          return _buildMushafPage(pageNumber, settings, quranProvider);
        },
      ),
    );
  }

  Widget _buildMushafPage(int pageNumber, SettingsProvider settings, QuranProvider quranProvider) {
    final List<Ayah>? ayahs = _pageCache[pageNumber];
    final Surah? firstSurahOnPage = quranProvider.getSurahInfoForPage(pageNumber);
    final String surahName = firstSurahOnPage?.name ?? " ";
    final int juzNumber = quranProvider.getJuzNumberForPage(pageNumber);

    return Container(
      margin: EdgeInsets.all(_isFullScreen ? 0 : (_showControls ? 16 : 8)), // تقليل الهامش عند إخفاء التحكم
      padding: const EdgeInsets.symmetric(vertical: 10), // حشوة رأسية للصفحة
      decoration: BoxDecoration(
        color: _getPageColor(settings),
        borderRadius: _isFullScreen ? null : BorderRadius.circular(12), // تقليل دائرية الزوايا
        boxShadow: _isFullScreen || !_showControls ? null : [ // إخفاء الظل عند إخفاء التحكم
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الصفحة (اسم السورة والجزء) - يظهر فقط إذا التحكم ظاهر
          if(_showControls && !_isFullScreen)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('الجزء $juzNumber', style: TextStyle(fontSize: settings.fontSize - 2, color: settings.getReadingTextColor().withOpacity(0.7))),
                  Text(surahName, style: TextStyle(fontSize: settings.fontSize - 2, fontWeight: FontWeight.bold, color: settings.getReadingTextColor())),
                ],
              ),
            ),
          Expanded(
            child: ClipRRect(
              borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(12),
              child: (_isLoadingPage && ayahs == null)
                  ? Center(child: CircularProgressIndicator(color: settings.getReadingAccentColor()))
                  : (ayahs == null || ayahs.isEmpty)
                      ? Center(child: Text('لا توجد بيانات لهذه الصفحة', style: TextStyle(color: settings.getReadingTextColor())))
                      : QuranTextView( // استخدام الويدجت الجديد لعرض النص
                          ayahs: ayahs,
                          settingsProvider: settings,
                          pageNumber: pageNumber,
                        ),
            ),
          ),
          // رقم الصفحة في الأسفل - يظهر فقط إذا التحكم ظاهر
          if(_showControls && !_isFullScreen)
            Padding(
              padding: const EdgeInsets.only(top: 10.0, bottom: 5.0),
              child: Text(
                '$pageNumber',
                style: TextStyle(
                  fontSize: settings.fontSize - 1,
                  fontWeight: FontWeight.bold,
                  color: settings.getReadingTextColor().withOpacity(0.8),
                ),
              ),
            ),
        ],
      ),
    );
  }


  Widget _buildTopBar(BuildContext context, SettingsProvider settings, QuranProvider quranProvider) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      top: _showControls ? 0 : - (MediaQuery.of(context).padding.top + 60), // إخفاء الشريط للأعلى
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 8,
          left: 16,
          right: 16,
          bottom: 12, // زيادة الحشوة السفلية قليلاً
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withOpacity(0.95), // لون أكثر شفافية
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            )
          ]
        ),
        child: RTLHelper.buildRTLRow(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: RTLHelper.buildRTLIcon(Icons.arrow_back_ios_new_rounded, color: Theme.of(context).colorScheme.onSurface),
              tooltip: "رجوع",
              onPressed: () => Navigator.pop(context),
            ),
            // يمكنك عرض اسم السورة أو الجزء هنا إذا أردت
             Text(
              'صفحة $_currentPage',
              style: TextStyle(
                fontSize: settings.fontSize,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface
              ),
            ),
            RTLHelper.buildRTLRow(
              children: [
                IconButton(
                  icon: Icon(Icons.bookmark_add_outlined, color: Theme.of(context).colorScheme.onSurface),
                  tooltip: "إضافة للمفضلة",
                  onPressed: () => _addCurrentPageToBookmark(quranProvider),
                ),
                IconButton(
                  icon: Icon(_isFullScreen ? Icons.fullscreen_exit_rounded : Icons.fullscreen_rounded, color: Theme.of(context).colorScheme.onSurface),
                  tooltip: _isFullScreen ? "الخروج من وضع ملء الشاشة" : "وضع ملء الشاشة",
                  onPressed: _toggleFullScreen,
                ),
                // IconButton(
                //   icon: Icon(Icons.settings_outlined, color: Theme.of(context).colorScheme.onSurface),
                //   onPressed: () => _openReadingSettings(context),
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context, SettingsProvider settings, QuranProvider quranProvider) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      bottom: _showControls ? 0 : -80, // إخفاء الشريط للأسفل
      left: 0,
      right: 0,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12), // تعديل الحشوة
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withOpacity(0.95),
          borderRadius: BorderRadius.circular(25), // زوايا دائرية أكثر
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, -2), // ظل للأعلى
            ),
          ],
        ),
        child: RTLHelper.buildRTLRow(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly, // توزيع متساوي
          children: [
            IconButton(
              icon: RTLHelper.buildRTLIcon(Icons.chevron_left_rounded, size: 28, color: Theme.of(context).colorScheme.primary),
              tooltip: "الصفحة السابقة",
              onPressed: _currentPage < _totalPages ? _nextPage : null, // عكس المنطق بسبب reverse: true
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // شريط تمرير لتغيير الصفحة
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      trackHeight: 2.0,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
                    ),
                    child: Slider(
                      value: _currentPage.toDouble(),
                      min: 1,
                      max: _totalPages.toDouble(),
                      divisions: _totalPages -1, // عدد التقسيمات
                      activeColor: Theme.of(context).colorScheme.primary,
                      inactiveColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      label: 'صفحة $_currentPage',
                      onChanged: (double value) {
                        setState(() {
                           // لا نغير الصفحة هنا مباشرة، فقط عند انتهاء التغيير
                        });
                      },
                      onChangeEnd: (double value) { // تغيير الصفحة عند انتهاء السحب
                        _pageController.animateToPage(
                          value.round() - 1,
                          duration: const Duration(milliseconds: 100),
                          curve: Curves.easeInOut,
                        );
                      },
                    ),
                  ),
                  Text(
                    'صفحة $_currentPage من $_totalPages',
                    style: TextStyle(
                      fontSize: settings.fontSize - 2,
                      fontWeight: FontWeight.w500,
                       color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8)
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              icon: RTLHelper.buildRTLIcon(Icons.chevron_right_rounded, size: 28, color: Theme.of(context).colorScheme.primary),
              tooltip: "الصفحة التالية",
              onPressed: _currentPage > 1 ? _previousPage : null, // عكس المنطق بسبب reverse: true
            ),
          ],
        ),
      ),
    );
  }


  Color _getBackgroundColor(SettingsProvider settings) {
    // يمكن تخصيص لون الخلفية العام هنا بناءً على إعدادات القراءة
    return settings.getReadingBackgroundColor(); 
  }

  Color _getPageColor(SettingsProvider settings) {
    // يمكن تخصيص لون خلفية الصفحة هنا
     return settings.getReadingPageColor();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
      if (_showControls) {
        _controlsAnimationController.reverse(); // إظهار (من 0 إلى 1 في الأنيميشن المعكوس)
      } else {
        _controlsAnimationController.forward(); // إخفاء (من 1 إلى 0 في الأنيميشن المعكوس)
      }
    });
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
      if (_isFullScreen) {
        _showControls = false; // إخفاء التحكم عند الدخول في وضع ملء الشاشة
        _controlsAnimationController.forward();
      } else {
        _showControls = true; // إظهار التحكم عند الخروج
         _controlsAnimationController.reverse();
      }
      // يمكنك إضافة كود للتحكم في شريط الحالة والنظام هنا إذا أردت
    });
  }

  void _previousPage() { // هذه الآن تذهب للصفحة ذات الرقم الأقل (لليسار في المصحف)
    if (_currentPage > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 250), // تقليل مدة الانتقال
        curve: Curves.easeOut, // منحنى أسرع
      );
    }
  }

  void _nextPage() { // هذه الآن تذهب للصفحة ذات الرقم الأعلى (لليمين في المصحف)
    if (_currentPage < _totalPages) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeOut,
      );
    }
  }

  void _addCurrentPageToBookmark(QuranProvider quranProvider) async {
    // إضافة الصفحة الحالية للمفضلة
    // QuranProvider يجب أن يوفر دالة لإضافة مفضلة بناءً على رقم الصفحة
    // أو يمكنك تحديد أول آية في الصفحة وحفظها كعلامة
    try {
        // مثال: حفظ أول آية في الصفحة كعلامة (تحتاج لتفاصيل الآية)
        // List<Ayah>? ayahsOnPage = _pageCache[_currentPage];
        // if (ayahsOnPage != null && ayahsOnPage.isNotEmpty) {
        //   Ayah firstAyah = ayahsOnPage.first;
        //   await quranProvider.addBookmark(firstAyah.surahNumber, firstAyah.numberInSurah);
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(content: Text('تم إضافة الآية ${firstAyah.numberInSurah} من سورة ${firstAyah.surahName} للمفضلة')),
        //   );
        // } else {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(content: Text('لا يمكن إضافة علامة، الصفحة فارغة أو غير محملة')),
        //   );
        // }
         ScaffoldMessenger.of(context).showSnackBar(
           SnackBar(content: Text('تم إضافة الصفحة $_currentPage للمفضلة (تحت الإنشاء)')),
         );

    } catch (e) {
         ScaffoldMessenger.of(context).showSnackBar(
           SnackBar(content: Text('خطأ في إضافة المفضلة: $e'), backgroundColor: Colors.red),
         );
    }
  }

  // void _openReadingSettings(BuildContext context) {
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(
  //       builder: (context) => const ReadingSettingsScreen(), // شاشة إعدادات القراءة
  //     ),
  //   );
  // }
}
