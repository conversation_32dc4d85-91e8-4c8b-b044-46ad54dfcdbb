import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../models/quran_models.dart';
import '../services/audio_service.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _isDarkModeKey = 'isDarkMode';
  static const String _fontSizeKey = 'fontSize';
  static const String _selectedReciterKey = 'selectedReciter';
  static const String _notificationsEnabledKey = 'notificationsEnabled';
  static const String _themeColorKey = 'themeColor';
  static const String _backgroundImageKey = 'backgroundImage';
  static const String _enableAnimationsKey = 'enableAnimations';
  static const String _accessibilityModeKey = 'accessibilityMode';
  static const String _readingModeKey = 'readingMode';
  static const String _fontFamilyKey = 'fontFamily';
  static const String _lineHeightKey = 'lineHeight';
  static const String _readingProgressKey = 'readingProgress';
  static const String _nightModeKey = 'nightMode';
  static const String _autoScrollKey = 'autoScroll';

  Box? _settingsBox;

  // Settings variables
  bool _isDarkMode = false;
  double _fontSize = 18.0;
  int _selectedReciterId = 1; // مشاري راشد العفاسي - رابط مجرب ويعمل
  bool _notificationsEnabled = true;
  String _themeColor = 'green'; // الثيم الافتراضي
  String _backgroundImage = 'simple'; // الخلفية الافتراضية
  bool _enableAnimations = true; // تفعيل الرسوم المتحركة
  bool _accessibilityMode = false; // وضع إمكانية الوصول لكبار السن
  bool _autoPlay = false;
  bool _showTranslation = true;
  bool _showTafsir = false;

  // إعدادات القراءة المتقدمة
  String _readingMode = 'normal'; // normal, night, sepia
  String _fontFamily = 'Amiri'; // Amiri, Scheherazade, Uthmanic
  double _lineHeight = 1.8;
  bool _nightMode = false;
  bool _autoScroll = false;
  Map<String, double> _readingProgress = {}; // تتبع تقدم القراءة لكل سورة
  TimeOfDay _morningAzkarTime = const TimeOfDay(hour: 6, minute: 0);
  TimeOfDay _eveningAzkarTime = const TimeOfDay(hour: 18, minute: 0);
  TimeOfDay _hadithReminderTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _quranReminderTime = const TimeOfDay(hour: 20, minute: 0);

  // Getters
  bool get isDarkMode => _isDarkMode;
  double get fontSize => _fontSize;
  int get selectedReciterId => _selectedReciterId;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get autoPlay => _autoPlay;
  bool get showTranslation => _showTranslation;
  bool get showTafsir => _showTafsir;
  TimeOfDay get morningAzkarTime => _morningAzkarTime;
  TimeOfDay get eveningAzkarTime => _eveningAzkarTime;
  TimeOfDay get hadithReminderTime => _hadithReminderTime;
  TimeOfDay get quranReminderTime => _quranReminderTime;
  String get themeColor => _themeColor;
  String get backgroundImage => _backgroundImage;
  bool get enableAnimations => _enableAnimations;
  bool get accessibilityMode => _accessibilityMode;

  // إعدادات القراءة المتقدمة
  String get readingMode => _readingMode;
  String get fontFamily => _fontFamily;
  double get lineHeight => _lineHeight;
  bool get nightMode => _nightMode;
  bool get autoScroll => _autoScroll;
  Map<String, double> get readingProgress => _readingProgress;

  Future<void> initialize() async {
    try {
      _settingsBox = await Hive.openBox('settings');
      await _loadSettings();
    } catch (e) {
      // If Hive fails, use default values and log error
      print('Settings initialization failed: $e');
      _useDefaultSettings();
      notifyListeners();
    }
  }

  void _useDefaultSettings() {
    _isDarkMode = false;
    _fontSize = 18.0;
    _selectedReciterId = 1;
    _notificationsEnabled = true;
    _autoPlay = false;
    _showTranslation = true;
    _showTafsir = false;
    _morningAzkarTime = const TimeOfDay(hour: 6, minute: 0);
    _eveningAzkarTime = const TimeOfDay(hour: 18, minute: 0);
    _hadithReminderTime = const TimeOfDay(hour: 9, minute: 0);
    _quranReminderTime = const TimeOfDay(hour: 20, minute: 0);
    _themeColor = 'green';
    _backgroundImage = 'simple';
    _enableAnimations = true;
    _accessibilityMode = false;
  }

  Future<void> _loadSettings() async {
    try {
      if (_settingsBox != null) {
        _isDarkMode = _settingsBox!.get(_isDarkModeKey, defaultValue: false);
        _fontSize = _settingsBox!.get(_fontSizeKey, defaultValue: 18.0);
        _selectedReciterId = _settingsBox!.get(_selectedReciterKey, defaultValue: 1);
        _notificationsEnabled = _settingsBox!.get(_notificationsEnabledKey, defaultValue: true);
        _autoPlay = _settingsBox!.get('autoPlay', defaultValue: false);
        _showTranslation = _settingsBox!.get('showTranslation', defaultValue: true);
        _showTafsir = _settingsBox!.get('showTafsir', defaultValue: false);

        // Load time settings
        final morningHour = _settingsBox!.get('morningAzkarTime_hour', defaultValue: 6);
        final morningMinute = _settingsBox!.get('morningAzkarTime_minute', defaultValue: 0);
        _morningAzkarTime = TimeOfDay(hour: morningHour, minute: morningMinute);

        final eveningHour = _settingsBox!.get('eveningAzkarTime_hour', defaultValue: 18);
        final eveningMinute = _settingsBox!.get('eveningAzkarTime_minute', defaultValue: 0);
        _eveningAzkarTime = TimeOfDay(hour: eveningHour, minute: eveningMinute);

        final hadithHour = _settingsBox!.get('hadithReminderTime_hour', defaultValue: 9);
        final hadithMinute = _settingsBox!.get('hadithReminderTime_minute', defaultValue: 0);
        _hadithReminderTime = TimeOfDay(hour: hadithHour, minute: hadithMinute);

        final quranHour = _settingsBox!.get('quranReminderTime_hour', defaultValue: 20);
        final quranMinute = _settingsBox!.get('quranReminderTime_minute', defaultValue: 0);
        _quranReminderTime = TimeOfDay(hour: quranHour, minute: quranMinute);

        // Load new theme settings
        _themeColor = _settingsBox!.get(_themeColorKey, defaultValue: 'green');
        _backgroundImage = _settingsBox!.get(_backgroundImageKey, defaultValue: 'simple');
        _enableAnimations = _settingsBox!.get(_enableAnimationsKey, defaultValue: true);
        _accessibilityMode = _settingsBox!.get(_accessibilityModeKey, defaultValue: false);

        // Load reading settings
        _readingMode = _settingsBox!.get(_readingModeKey, defaultValue: 'normal');
        _fontFamily = _settingsBox!.get(_fontFamilyKey, defaultValue: 'Amiri');
        _lineHeight = _settingsBox!.get(_lineHeightKey, defaultValue: 1.8);
        _nightMode = _settingsBox!.get(_nightModeKey, defaultValue: false);
        _autoScroll = _settingsBox!.get(_autoScrollKey, defaultValue: false);

        // Load reading progress
        final progressData = _settingsBox!.get(_readingProgressKey, defaultValue: <String, dynamic>{});
        _readingProgress = Map<String, double>.from(progressData);
      }
      notifyListeners();
    } catch (e) {
      // Use default values if loading fails
    }
  }

  Future<void> setDarkMode(bool value) async {
    _isDarkMode = value;
    try {
      await _settingsBox?.put(_isDarkModeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setFontSize(double value) async {
    _fontSize = value;
    try {
      await _settingsBox?.put(_fontSizeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setSelectedReciter(int reciterId) async {
    _selectedReciterId = reciterId;
    try {
      await _settingsBox?.put(_selectedReciterKey, reciterId);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  // الحصول على القارئ المحدد من قائمة القراء
  Reciter? getSelectedReciter() {
    try {
      final reciters = AudioService.getDefaultReciters();
      return reciters.firstWhere(
        (reciter) => reciter.id == _selectedReciterId,
        orElse: () => reciters.first, // مشاري العفاسي كافتراضي
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> setNotificationsEnabled(bool value) async {
    _notificationsEnabled = value;
    try {
      await _settingsBox?.put(_notificationsEnabledKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setAutoPlay(bool value) async {
    _autoPlay = value;
    try {
      await _settingsBox?.put('autoPlay', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setShowTranslation(bool value) async {
    _showTranslation = value;
    try {
      await _settingsBox?.put('showTranslation', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setShowTafsir(bool value) async {
    _showTafsir = value;
    try {
      await _settingsBox?.put('showTafsir', value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setMorningAzkarTime(TimeOfDay time) async {
    _morningAzkarTime = time;
    try {
      await _settingsBox?.put('morningAzkarTime_hour', time.hour);
      await _settingsBox?.put('morningAzkarTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setEveningAzkarTime(TimeOfDay time) async {
    _eveningAzkarTime = time;
    try {
      await _settingsBox?.put('eveningAzkarTime_hour', time.hour);
      await _settingsBox?.put('eveningAzkarTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setHadithReminderTime(TimeOfDay time) async {
    _hadithReminderTime = time;
    try {
      await _settingsBox?.put('hadithReminderTime_hour', time.hour);
      await _settingsBox?.put('hadithReminderTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setQuranReminderTime(TimeOfDay time) async {
    _quranReminderTime = time;
    try {
      await _settingsBox?.put('quranReminderTime_hour', time.hour);
      await _settingsBox?.put('quranReminderTime_minute', time.minute);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  // New theme setters
  Future<void> setThemeColor(String themeColor) async {
    _themeColor = themeColor;
    try {
      await _settingsBox?.put(_themeColorKey, themeColor);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setBackgroundImage(String backgroundImage) async {
    _backgroundImage = backgroundImage;
    try {
      await _settingsBox?.put(_backgroundImageKey, backgroundImage);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setEnableAnimations(bool value) async {
    _enableAnimations = value;
    try {
      await _settingsBox?.put(_enableAnimationsKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setAccessibilityMode(bool value) async {
    _accessibilityMode = value;
    try {
      await _settingsBox?.put(_accessibilityModeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  // إعدادات القراءة المتقدمة
  Future<void> setReadingMode(String mode) async {
    _readingMode = mode;
    try {
      await _settingsBox?.put(_readingModeKey, mode);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setFontFamily(String fontFamily) async {
    _fontFamily = fontFamily;
    try {
      await _settingsBox?.put(_fontFamilyKey, fontFamily);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setLineHeight(double lineHeight) async {
    _lineHeight = lineHeight;
    try {
      await _settingsBox?.put(_lineHeightKey, lineHeight);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setNightMode(bool value) async {
    _nightMode = value;
    try {
      await _settingsBox?.put(_nightModeKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> setAutoScroll(bool value) async {
    _autoScroll = value;
    try {
      await _settingsBox?.put(_autoScrollKey, value);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  Future<void> updateReadingProgress(String surahName, double progress) async {
    _readingProgress[surahName] = progress;
    try {
      await _settingsBox?.put(_readingProgressKey, _readingProgress);
    } catch (e) {
      // Handle error silently
    }
    notifyListeners();
  }

  double getReadingProgress(String surahName) {
    return _readingProgress[surahName] ?? 0.0;
  }

  Future<void> resetToDefaults() async {
    _isDarkMode = false;
    _fontSize = 18.0;
    _selectedReciterId = 1;
    _notificationsEnabled = true;
    _themeColor = 'green';
    _backgroundImage = 'simple';
    _enableAnimations = true;
    _accessibilityMode = false;

    try {
      await _settingsBox?.clear();
      await _settingsBox?.put(_isDarkModeKey, _isDarkMode);
      await _settingsBox?.put(_fontSizeKey, _fontSize);
      await _settingsBox?.put(_selectedReciterKey, _selectedReciterId);
      await _settingsBox?.put(_notificationsEnabledKey, _notificationsEnabled);
      await _settingsBox?.put(_themeColorKey, _themeColor);
      await _settingsBox?.put(_backgroundImageKey, _backgroundImage);
      await _settingsBox?.put(_enableAnimationsKey, _enableAnimations);
      await _settingsBox?.put(_accessibilityModeKey, _accessibilityMode);
    } catch (e) {
      // Handle error silently
    }

    notifyListeners();
  }

  // Helper method to get font size category
  String getFontSizeCategory() {
    if (_fontSize <= 14) return 'صغير';
    if (_fontSize <= 18) return 'متوسط';
    if (_fontSize <= 22) return 'كبير';
    return 'كبير جداً';
  }

  double getNextFontSize() {
    if (_fontSize < 28) {
      return _fontSize + 2;
    }
    return _fontSize;
  }

  double getPreviousFontSize() {
    if (_fontSize > 12) {
      return _fontSize - 2;
    }
    return _fontSize;
  }



  // Helper method to format time for display
  String formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // دوال مساعدة لألوان القراءة
  Color getReadingBackgroundColor() {
    switch (_readingMode) {
      case 'night':
        return const Color(0xFF1A1A1A);
      case 'sepia':
        return const Color(0xFFF4F1E8);
      default:
        return Colors.white;
    }
  }

  Color getReadingTextColor() {
    switch (_readingMode) {
      case 'night':
        return const Color(0xFFE0E0E0);
      case 'sepia':
        return const Color(0xFF5D4037);
      default:
        return const Color(0xFF212121);
    }
  }

  Color getReadingAccentColor() {
    switch (_readingMode) {
      case 'night':
        return const Color(0xFF4CAF50);
      case 'sepia':
        return const Color(0xFF8D6E63);
      default:
        return const Color(0xFF2196F3);
    }
  }

  // دوال مساعدة للخطوط
  String getFontFamilyName() {
    switch (_fontFamily) {
      case 'Scheherazade':
        return 'Scheherazade New';
      case 'Uthmanic':
        return 'KFGQPC Uthmanic Script HAFS';
      default:
        return 'Amiri';
    }
  }

  List<String> getAvailableFonts() {
    return ['Amiri', 'Scheherazade', 'Uthmanic'];
  }

  List<String> getAvailableReadingModes() {
    return ['normal', 'night', 'sepia'];
  }

  String getReadingModeDisplayName(String mode) {
    switch (mode) {
      case 'night':
        return 'الوضع الليلي';
      case 'sepia':
        return 'وضع السيبيا';
      default:
        return 'الوضع العادي';
    }
  }
}
