import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart'; // افترض وجود هذا الملف
import '../../providers/settings_provider.dart'; // افترض وجود هذا الملف

import '../../widgets/animated_card.dart'; // افترض وجود هذا الملف
import '../../utils/rtl_helper.dart'; // افترض وجود هذا الملف
import '../../utils/responsive_helper.dart'; // افترض وجود هذا الملف
import 'enhanced_mushaf_reader.dart';
import 'surah_reading_screen.dart';
// قد تحتاج إلى استيراد شاشة عرض الأجزاء إذا تم تفعيلها
// import 'juz_display_screen.dart'; 
import 'quran_bookmarks_screen.dart';


class RedesignedMushafScreen extends StatefulWidget {
  const RedesignedMushafScreen({super.key});

  @override
  State<RedesignedMushafScreen> createState() => _RedesignedMushafScreenState();
}

class _RedesignedMushafScreenState extends State<RedesignedMushafScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;

  final ScrollController _scrollController = ScrollController();
  // 0: قائمة السور، 1: مصحف صفحات، (يمكن إضافة المزيد مثل الأجزاء)
  // تم تغيير _selectedViewMode ليكون أكثر وضوحًا
  int _currentMainView = 0; // 0: تصفح (سور/أجزاء)، 1: مصحف صفحات
  String _searchQuery = '';
  bool _showSearch = false;

  // لتتبع عرض السور (قائمة أو شبكة)
  int _surahDisplayMode = 0; // 0: قائمة، 1: شبكة

  @override
  void initState() {
    super.initState();
    // تبويبات مبسطة: السور، المصحف، المفضلة. يمكن إضافة "الأجزاء" لاحقًا.
    _tabController = TabController(length: 3, vsync: this); 
    _tabController.addListener(() {
      // تحديث _currentMainView بناءً على التبويب المختار
      // إذا كان التبويب الثاني (المصحف) هو المختار، غير العرض الرئيسي
      if (_tabController.index == 1) {
        if (_currentMainView != 1) {
          setState(() {
            _currentMainView = 1;
          });
        }
      } else {
        if (_currentMainView != 0) {
          setState(() {
            _currentMainView = 0;
          });
        }
      }
    });

    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _headerAnimationController.forward();

    // تحميل بيانات القرآن عند التهيئة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<QuranProvider>(context, listen: false).initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _headerAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, SettingsProvider>(
      builder: (context, quranProvider, settings, child) {
        return Scaffold(
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                _buildSliverAppBar(context, settings, quranProvider),
                if (_currentMainView == 0) // عرض البحث ومحددات العرض فقط في وضع التصفح
                  _buildSearchAndDisplayControls(context, settings),
                _buildTabBar(context, settings), // شريط التبويبات دائمًا ظاهر
                _buildContent(context, quranProvider, settings),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar(BuildContext context, SettingsProvider settings, QuranProvider quranProvider) {
    String title = 'المصحف الشريف';
    String subtitle = 'القرآن الكريم كاملاً';

    if (_currentMainView == 1) {
      title = 'مصحف المدينة';
      subtitle = 'صفحة ${quranProvider.currentMushafPage ?? 1}'; // مثال، يجب ربطه بالصفحة الحالية في المصحف
    } else if (_tabController.index == 0) {
       title = 'فهرس السور';
       subtitle = 'تصفح سور القرآن الكريم';
    } else if (_tabController.index == 2) {
       title = 'العلامات المرجعية';
       subtitle = 'آياتك المحفوظة';
    }


    return SliverAppBar(
      expandedHeight: 180, // تقليل الارتفاع قليلاً
      floating: false,
      pinned: true,
      elevation: 2,
      backgroundColor: Theme.of(context).colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _headerAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withOpacity(0.8),
                    Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // خلفية زخرفية مبسطة
                  Positioned(
                    top: -40 * (1-_headerAnimation.value),
                    left: -40 * (1-_headerAnimation.value),
                    child: Opacity(
                      opacity: _headerAnimation.value,
                      child: Icon(Icons.brightness_low_outlined, size: 120, color: Colors.white.withOpacity(0.05)),
                    ),
                  ),
                  Positioned(
                    bottom: -30 * (1-_headerAnimation.value),
                    right: -30 * (1-_headerAnimation.value),
                     child: Opacity(
                      opacity: _headerAnimation.value,
                      child: Icon(Icons.brightness_high_outlined, size: 100, color: Colors.white.withOpacity(0.05)),
                    ),
                  ),
                  Center(
                    child: FadeTransition(
                      opacity: _headerAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(height: 30), // مسافة من الأعلى
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Icon(
                              _currentMainView == 1 ? Icons.auto_stories_rounded : Icons.menu_book_rounded,
                              size: 40,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 12),
                          RTLHelper.buildDirectionalText(
                            title,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: settings.fontSize + 6,
                              fontWeight: FontWeight.bold,
                              shadows: [Shadow(blurRadius: 2, color: Colors.black.withOpacity(0.2), offset: const Offset(1,1))]
                            ),
                            forceRTL: true,
                          ),
                          const SizedBox(height: 6),
                          RTLHelper.buildDirectionalText(
                            subtitle,
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.85),
                              fontSize: settings.fontSize -1,
                            ),
                            forceRTL: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      actions: [
        if (_currentMainView == 0) // زر البحث فقط في وضع التصفح
          IconButton(
            icon: Icon(
              _showSearch ? Icons.close : Icons.search,
              color: Colors.white,
            ),
            tooltip: _showSearch ? "إغلاق البحث" : "بحث",
            onPressed: () {
              setState(() {
                _showSearch = !_showSearch;
                if (!_showSearch) _searchQuery = '';
              });
            },
          ),
        IconButton(
          icon: const Icon(Icons.settings_outlined, color: Colors.white),
          tooltip: "الإعدادات",
          onPressed: () {
            // انتقل إلى شاشة الإعدادات
            // Navigator.push(context, MaterialPageRoute(builder: (context) => SettingsScreen()));
             ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('شاشة الإعدادات (تحت الإنشاء)')));
          },
        ),
      ],
    );
  }
  
  // ويدجت جديد لتجميع البحث ومحددات العرض
  Widget _buildSearchAndDisplayControls(BuildContext context, SettingsProvider settings) {
    return SliverList(
      delegate: SliverChildListDelegate([
        if (_showSearch) _buildSearchBar(context, settings),
        if (_tabController.index == 0) // عرض محددات عرض السور فقط في تبويب السور
         _buildSurahDisplayModeSelector(context, settings),
      ]),
    );
  }


  Widget _buildSearchBar(BuildContext context, SettingsProvider settings) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor, // استخدام لون البطاقة للتباين
        borderRadius: BorderRadius.circular(25), // زوايا أكثر دائرية
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        textDirection: TextDirection.rtl,
        style: TextStyle(fontSize: settings.fontSize),
        decoration: InputDecoration(
          hintText: 'ابحث في السور...',
          hintStyle: TextStyle(fontSize: settings.fontSize, color: Theme.of(context).hintColor),
          prefixIcon: Icon(Icons.search, color: Theme.of(context).colorScheme.primary),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Theme.of(context).cardColor,
          contentPadding: const EdgeInsets.symmetric(vertical: 14),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildSurahDisplayModeSelector(BuildContext context, SettingsProvider settings) {
    // محدد عرض السور (قائمة/شبكة)
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SegmentedButton<int>(
        segments: <ButtonSegment<int>>[
          ButtonSegment<int>(
              value: 0,
              label: Text('قائمة', style: TextStyle(fontSize: settings.fontSize -2)),
              icon: const Icon(Icons.list_rounded)),
          ButtonSegment<int>(
              value: 1,
              label: Text('شبكة', style: TextStyle(fontSize: settings.fontSize-2)),
              icon: const Icon(Icons.grid_view_rounded)),
        ],
        selected: <int>{_surahDisplayMode},
        onSelectionChanged: (Set<int> newSelection) {
          setState(() {
            _surahDisplayMode = newSelection.first;
          });
        },
        style: SegmentedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.surface.withOpacity(0.5),
          selectedForegroundColor: Colors.white,
          selectedBackgroundColor: Theme.of(context).colorScheme.primary,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    );
  }


  Widget _buildTabBar(BuildContext context, SettingsProvider settings) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _SliverTabBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Theme.of(context).hintColor,
          indicatorColor: Theme.of(context).colorScheme.primary,
          indicatorWeight: 2.5,
          labelStyle: TextStyle(
            fontSize: settings.fontSize -1,
            fontWeight: FontWeight.w600, // خط أثقل للتبويب المختار
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: settings.fontSize -2,
            fontWeight: FontWeight.normal,
          ),
          tabs: [
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.menu_book_outlined, size: 20), // أيقونة أفتح
                  const SizedBox(width: 6),
                  const Text('السور'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.auto_stories_outlined, size: 20), // أيقونة أفتح
                  const SizedBox(width: 6),
                  const Text('المصحف'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.bookmark_outline_rounded, size: 20), // أيقونة أفتح
                  const SizedBox(width: 6),
                  const Text('المفضلة'),
                ],
              ),
            ),
            // يمكن إضافة تبويب الأجزاء هنا لاحقًا
            // Tab(text: 'الأجزاء', icon: Icon(Icons.library_books_outlined)),
          ],
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor, // لون خلفية الشاشة
      ),
    );
  }

  Widget _buildContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    // إذا كان عرض المصحف مفعلًا، اعرض قارئ المصحف مباشرة
    if (_currentMainView == 1) {
      return SliverFillRemaining(
        child: EnhancedMushafReader(
          initialPage: quranProvider.lastReadMushafPage, // ابدأ من آخر صفحة مقروءة
        ),
      );
    }

    // وإلا، اعرض محتوى التبويبات
    return SliverFillRemaining(
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildSurahsContent(context, quranProvider, settings), // محتوى تبويب السور
          Container(), // هذا التبويب سيعرض EnhancedMushafReader مباشرة بسبب المنطق أعلاه
          _buildBookmarksContent(context, quranProvider, settings), // محتوى تبويب المفضلة
          // _buildJuzContent(context, quranProvider, settings), // محتوى تبويب الأجزاء (إذا أضيف)
        ],
      ),
    );
  }

  Widget _buildSurahsContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    if (quranProvider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (quranProvider.errorMessage != null) {
      return Center(child: Text('خطأ: ${quranProvider.errorMessage}'));
    }

    final filteredSurahs = _getFilteredSurahs(quranProvider.surahs);

    if (filteredSurahs.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'لا توجد سور تطابق البحث "${_searchQuery}"',
          style: TextStyle(fontSize: settings.fontSize, color: Theme.of(context).hintColor),
        ),
      );
    }
    if (filteredSurahs.isEmpty) {
      return Center(
        child: Text(
          'لا توجد سور متاحة حاليًا.',
          style: TextStyle(fontSize: settings.fontSize, color: Theme.of(context).hintColor),
        ),
      );
    }


    return _surahDisplayMode == 0
        ? _buildSurahsList(filteredSurahs, quranProvider, settings)
        : _buildSurahsGrid(filteredSurahs, quranProvider, settings);
  }

  Widget _buildSurahsList(List<dynamic> surahs, QuranProvider quranProvider, SettingsProvider settings) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahCard(surah, index, quranProvider, settings);
      },
    );
  }

  Widget _buildSurahsGrid(List<dynamic> surahs, QuranProvider quranProvider, SettingsProvider settings) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: ResponsiveHelper.getCrossAxisCountWithParams(context, mobileColumns: 2, tabletColumns: 3, desktopColumns: 4),
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.9, // تعديل النسبة لتناسب المحتوى بشكل أفضل
      ),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahGridCard(surah, index, quranProvider, settings);
      },
    );
  }

  Widget _buildBookmarksContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
     // استخدام شاشة العلامات المرجعية الموجودة
    return const QuranBookmarksScreen();
  }


  Widget _buildSurahCard(dynamic surah, int index, QuranProvider quranProvider, SettingsProvider settings) {
    final theme = Theme.of(context);
    final isLastRead = surah.number == quranProvider.lastReadSurahNumber && quranProvider.lastReadAyahNumber != null;

    return AnimatedListCard( // استخدام الويدجت المتحرك
      index: index,
      onTap: () => _navigateToSurah(surah, quranProvider),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16), // زوايا أكثر دائرية
          border: isLastRead ? Border.all(color: theme.colorScheme.primary, width: 1.5) : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.07),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: RTLHelper.buildRTLRow(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // رقم السورة بتصميم محدث
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.primary.withOpacity(0.7)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle, // شكل دائري
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: settings.fontSize, // حجم خط أصغر قليلاً ليتناسب
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RTLHelper.buildDirectionalText(
                      surah.name,
                      style: TextStyle(
                        fontSize: settings.fontSize + 3,
                        fontWeight: FontWeight.w600, // خط أثقل قليلاً
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                      forceRTL: true,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      surah.englishNameTranslation,
                      style: TextStyle(
                        fontSize: settings.fontSize -1,
                        color: theme.hintColor, // لون أفتح للترجمة
                      ),
                    ),
                    const SizedBox(height: 8),
                    RTLHelper.buildRTLRow(
                      children: [
                        _buildInfoChip('${surah.numberOfAyahs} آية', Icons.format_list_numbered_rtl_rounded, theme, settings),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                          surah.revelationType == 'Meccan' ? Icons.nightlight_round : Icons.wb_sunny_rounded, // أيقونات معبرة
                          theme,
                          settings
                        ),
                      ],
                    ),
                     if (isLastRead && quranProvider.lastReadAyahNumber != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          'آخر قراءة: آية ${quranProvider.lastReadAyahNumber}',
                          style: TextStyle(
                            fontSize: settings.fontSize - 3,
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios_rounded, color: theme.hintColor.withOpacity(0.7), size: 18),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSurahGridCard(dynamic surah, int index, QuranProvider quranProvider, SettingsProvider settings) {
    final theme = Theme.of(context);
    final isLastRead = surah.number == quranProvider.lastReadSurahNumber && quranProvider.lastReadAyahNumber != null;

    return AnimatedListCard(
      index: index,
      onTap: () => _navigateToSurah(surah, quranProvider),
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: isLastRead ? Border.all(color: theme.colorScheme.primary, width: 1.5) : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.07),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12), // تقليل الحشوة قليلاً للشبكة
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center, // توسيط المحتوى
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 45, // حجم أصغر للأيقونة في الشبكة
                height: 45,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.primary.withOpacity(0.7)],
                  ),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: settings.fontSize -1,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              RTLHelper.buildDirectionalText(
                surah.name,
                style: TextStyle(
                  fontSize: settings.fontSize + 1,
                  fontWeight: FontWeight.w600,
                  color: theme.textTheme.bodyLarge?.color,
                ),
                forceRTL: true,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                surah.englishNameTranslation,
                style: TextStyle(
                  fontSize: settings.fontSize - 3,
                  color: theme.hintColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 6),
              Text(
                '${surah.numberOfAyahs} آية',
                style: TextStyle(
                  fontSize: settings.fontSize - 3,
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isLastRead && quranProvider.lastReadAyahNumber != null)
                Padding(
                  padding: const EdgeInsets.only(top: 6.0),
                  child: Text(
                    'آية ${quranProvider.lastReadAyahNumber}',
                    style: TextStyle(
                      fontSize: settings.fontSize - 4,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildInfoChip(String text, IconData icon, ThemeData theme, SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1), // لون أفتح للخلفية
        borderRadius: BorderRadius.circular(20), // زوايا دائرية أكثر
      ),
      child: RTLHelper.buildRTLRow(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.primary),
          const SizedBox(width: 5),
          Text(
            text,
            style: TextStyle(
              fontSize: settings.fontSize - 3, // حجم خط أصغر للمعلومات
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSurah(dynamic surah, QuranProvider quranProvider) async {
    // تحميل بيانات السورة الكاملة قبل الانتقال إذا لم تكن محملة
    // (QuranProvider يجب أن يوفر طريقة لتحميل تفاصيل السورة عند الطلب)
    // quranProvider.setCurrentSurah(surah); // قد لا يكون هذا كافيًا إذا كانت البيانات غير كاملة

    // افترض أن quranProvider.loadSurahDetails(surah.number) يعيد Surah object كامل
    final fullSurahData = await quranProvider.loadSurah(surah.number);

    if (mounted && fullSurahData != null) {
       quranProvider.setLastReadSurah(fullSurahData.number, null); // سجل السورة كآخر سورة تم فتحها
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SurahReadingScreen(surah: fullSurahData),
        ),
      );
    } else if (mounted) {
       ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لم يتم العثور على تفاصيل السورة')));
    }
  }

  List<dynamic> _getFilteredSurahs(List<dynamic> surahs) {
    if (_searchQuery.isEmpty) return surahs;

    final lowerCaseQuery = _searchQuery.toLowerCase();
    return surahs.where((surah) {
      // البحث بالاسم العربي، الإنجليزي، أو الترجمة الإنجليزية
      return surah.name.contains(_searchQuery) || // البحث بالاسم العربي كما هو
             surah.englishName.toLowerCase().contains(lowerCaseQuery) ||
             surah.englishNameTranslation.toLowerCase().contains(lowerCaseQuery) ||
             surah.number.toString().contains(_searchQuery); // البحث برقم السورة
    }).toList();
  }
}

// Helper class for SliverPersistentHeader with TabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverTabBarDelegate(this.tabBar, {required this.backgroundColor});

  final TabBar tabBar;
  final Color backgroundColor;

  @override
  double get minExtent => tabBar.preferredSize.height;
  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: backgroundColor, // استخدام لون خلفية الشاشة
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar || backgroundColor != oldDelegate.backgroundColor;
  }
}
