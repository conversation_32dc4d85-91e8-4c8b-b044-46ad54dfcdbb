import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/quran_provider.dart';
import '../../providers/settings_provider.dart';

import '../../widgets/animated_card.dart';
import '../../utils/rtl_helper.dart';
import '../../utils/responsive_helper.dart';
import 'enhanced_mushaf_reader.dart';
import 'surah_reading_screen.dart';

class RedesignedMushafScreen extends StatefulWidget {
  const RedesignedMushafScreen({super.key});

  @override
  State<RedesignedMushafScreen> createState() => _RedesignedMushafScreenState();
}

class _RedesignedMushafScreenState extends State<RedesignedMushafScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;

  final ScrollController _scrollController = ScrollController();
  int _selectedViewMode = 0; // 0: قائمة، 1: شبكة، 2: مصحف
  String _searchQuery = '';
  bool _showSearch = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _headerAnimationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _headerAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<QuranProvider, SettingsProvider>(
      builder: (context, quranProvider, settings, child) {
        return Scaffold(
          body: RTLHelper.buildResponsiveRTLLayout(
            context: context,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                _buildSliverAppBar(context, settings),
                _buildQuickActions(context, settings),
                _buildViewModeSelector(context, settings),
                _buildTabBar(context, settings),
                _buildContent(context, quranProvider, settings),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar(BuildContext context, SettingsProvider settings) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _headerAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                    Theme.of(context).colorScheme.primaryContainer,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // خلفية زخرفية
                  Positioned(
                    top: -50,
                    left: -50,
                    child: Transform.scale(
                      scale: _headerAnimation.value,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -30,
                    right: -30,
                    child: Transform.scale(
                      scale: _headerAnimation.value,
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.05),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),

                  // المحتوى الرئيسي
                  Center(
                    child: FadeTransition(
                      opacity: _headerAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(height: 40),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.menu_book_rounded,
                              size: 48,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          RTLHelper.buildDirectionalText(
                            'المصحف الشريف',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: settings.fontSize + 8,
                              fontWeight: FontWeight.bold,
                            ),
                            forceRTL: true,
                          ),
                          const SizedBox(height: 8),
                          RTLHelper.buildDirectionalText(
                            'القرآن الكريم كاملاً',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: settings.fontSize,
                            ),
                            forceRTL: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _showSearch ? Icons.close : Icons.search,
            color: Colors.white,
          ),
          onPressed: () {
            setState(() {
              _showSearch = !_showSearch;
              if (!_showSearch) _searchQuery = '';
            });
          },
        ),
        IconButton(
          icon: const Icon(Icons.bookmark, color: Colors.white),
          onPressed: () => _tabController.animateTo(3),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, SettingsProvider settings) {
    if (!_showSearch) return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          textDirection: TextDirection.rtl,
          decoration: InputDecoration(
            hintText: 'ابحث في السور والآيات...',
            hintStyle: TextStyle(fontSize: settings.fontSize),
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildViewModeSelector(BuildContext context, SettingsProvider settings) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: RTLHelper.buildRTLRow(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildViewModeButton(
              icon: Icons.list_rounded,
              label: 'قائمة',
              index: 0,
              settings: settings,
            ),
            _buildViewModeButton(
              icon: Icons.grid_view_rounded,
              label: 'شبكة',
              index: 1,
              settings: settings,
            ),
            _buildViewModeButton(
              icon: Icons.auto_stories_rounded,
              label: 'مصحف',
              index: 2,
              settings: settings,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildViewModeButton({
    required IconData icon,
    required String label,
    required int index,
    required SettingsProvider settings,
  }) {
    final isSelected = _selectedViewMode == index;
    final theme = Theme.of(context);

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedViewMode = index;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : theme.colorScheme.primary,
                  fontSize: settings.fontSize - 2,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, SettingsProvider settings) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Theme.of(context).colorScheme.primary,
          indicator: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          labelStyle: TextStyle(
            fontSize: settings.fontSize - 2,
            fontWeight: FontWeight.bold,
          ),
          tabs: [
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.menu_book_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('السور'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.library_books_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('الأجزاء'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.article_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('الصفحات'),
                ],
              ),
            ),
            Tab(
              child: RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.bookmark_rounded, size: 18),
                  const SizedBox(width: 4),
                  const Text('المفضلة'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return SliverFillRemaining(
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildSurahsContent(context, quranProvider, settings),
          _buildJuzContent(context, quranProvider, settings),
          _buildPagesContent(context, quranProvider, settings),
          _buildBookmarksContent(context, quranProvider, settings),
        ],
      ),
    );
  }

  Widget _buildSurahsContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    if (quranProvider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final filteredSurahs = _getFilteredSurahs(quranProvider.surahs);

    if (_selectedViewMode == 2) {
      // عرض المصحف
      return const EnhancedMushafReader();
    }

    return _selectedViewMode == 0
        ? _buildSurahsList(filteredSurahs, quranProvider, settings)
        : _buildSurahsGrid(filteredSurahs, quranProvider, settings);
  }

  Widget _buildSurahsList(List<dynamic> surahs, QuranProvider quranProvider, SettingsProvider settings) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahCard(surah, index, quranProvider, settings);
      },
    );
  }

  Widget _buildSurahsGrid(List<dynamic> surahs, QuranProvider quranProvider, SettingsProvider settings) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: ResponsiveHelper.getCrossAxisCountWithParams(context, mobileColumns: 2, tabletColumns: 3),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahGridCard(surah, index, quranProvider, settings);
      },
    );
  }

  Widget _buildJuzContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.library_books_rounded, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('قائمة الأجزاء', style: TextStyle(fontSize: settings.fontSize + 2)),
          Text('قريباً...', style: TextStyle(fontSize: settings.fontSize)),
        ],
      ),
    );
  }

  Widget _buildPagesContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.article_rounded, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('قائمة الصفحات', style: TextStyle(fontSize: settings.fontSize + 2)),
          Text('قريباً...', style: TextStyle(fontSize: settings.fontSize)),
        ],
      ),
    );
  }

  Widget _buildBookmarksContent(BuildContext context, QuranProvider quranProvider, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bookmark_rounded, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('المفضلة', style: TextStyle(fontSize: settings.fontSize + 2)),
          Text('لا توجد مفضلة محفوظة', style: TextStyle(fontSize: settings.fontSize)),
        ],
      ),
    );
  }

  Widget _buildSurahCard(dynamic surah, int index, QuranProvider quranProvider, SettingsProvider settings) {
    final theme = Theme.of(context);
    final isLastRead = surah.number == quranProvider.lastReadSurah;

    return AnimatedListCard(
      index: index,
      onTap: () => _navigateToSurah(surah, quranProvider),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isLastRead
                ? [theme.colorScheme.primary.withValues(alpha: 0.1), theme.colorScheme.primaryContainer.withValues(alpha: 0.1)]
                : [theme.colorScheme.surface, theme.colorScheme.surface],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ),
          borderRadius: BorderRadius.circular(20),
          border: isLastRead ? Border.all(color: theme.colorScheme.primary, width: 2) : null,
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: RTLHelper.buildRTLRow(
            children: [
              // رقم السورة
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: settings.fontSize + 2,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // معلومات السورة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RTLHelper.buildDirectionalText(
                      surah.name,
                      style: TextStyle(
                        fontSize: settings.fontSize + 4,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      forceRTL: true,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      surah.englishNameTranslation,
                      style: TextStyle(
                        fontSize: settings.fontSize,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    RTLHelper.buildRTLRow(
                      children: [
                        _buildInfoChip('${surah.numberOfAyahs} آية', Icons.format_list_numbered, theme),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                          surah.revelationType == 'Meccan' ? Icons.location_on : Icons.location_city,
                          theme,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // مؤشر تقدم القراءة
                    _buildReadingProgress(surah.name, settings),
                  ],
                ),
              ),

              // أزرار الإجراءات
              Column(
                children: [
                  _buildActionButton(Icons.bookmark_border, () => _toggleBookmark(surah), theme),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSurahGridCard(dynamic surah, int index, QuranProvider quranProvider, SettingsProvider settings) {
    final theme = Theme.of(context);
    final isLastRead = surah.number == quranProvider.lastReadSurah;

    return AnimatedListCard(
      index: index,
      onTap: () => _navigateToSurah(surah, quranProvider),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isLastRead
                ? [theme.colorScheme.primary.withValues(alpha: 0.2), theme.colorScheme.primaryContainer.withValues(alpha: 0.2)]
                : [theme.colorScheme.surface, theme.colorScheme.surface.withValues(alpha: 0.8)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(20),
          border: isLastRead ? Border.all(color: theme.colorScheme.primary, width: 2) : null,
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // رقم السورة
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: settings.fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // معلومات السورة
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    RTLHelper.buildDirectionalText(
                      surah.name,
                      style: TextStyle(
                        fontSize: settings.fontSize + 2,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      forceRTL: true,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      surah.englishNameTranslation,
                      style: TextStyle(
                        fontSize: settings.fontSize - 2,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${surah.numberOfAyahs} آية',
                      style: TextStyle(
                        fontSize: settings.fontSize - 2,
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              RTLHelper.buildRTLRow(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildSmallActionButton(Icons.bookmark_border, () => _toggleBookmark(surah), theme),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: RTLHelper.buildRTLRow(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.primary),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, VoidCallback onPressed, ThemeData theme) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [theme.colorScheme.primary, theme.colorScheme.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white, size: 20),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildSmallActionButton(IconData icon, VoidCallback onPressed, ThemeData theme) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: IconButton(
        icon: Icon(icon, color: theme.colorScheme.primary, size: 16),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildReadingProgress(String surahName, SettingsProvider settings) {
    final progress = settings.getReadingProgress(surahName);
    final theme = Theme.of(context);

    if (progress == 0.0) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        RTLHelper.buildRTLRow(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'تقدم القراءة',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          minHeight: 3,
        ),
      ],
    );
  }

  void _navigateToSurah(dynamic surah, QuranProvider quranProvider) async {
    quranProvider.setCurrentSurah(surah);

    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            SurahReadingScreen(surah: surah),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            )),
            child: child,
          );
        },
      ),
    );
  }

  void _toggleBookmark(dynamic surah) async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    try {
      // Check if the first ayah of the surah is bookmarked (representing the whole surah)
      final isBookmarked = await quranProvider.isBookmarked(surah.number, 1);

      if (isBookmarked) {
        await quranProvider.removeBookmark(surah.number, 1);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إزالة سورة ${surah.name} من المفضلة'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        await quranProvider.addBookmark(surah.number, 1);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إضافة سورة ${surah.name} للمفضلة'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  List<dynamic> _getFilteredSurahs(List<dynamic> surahs) {
    if (_searchQuery.isEmpty) return surahs;

    return surahs.where((surah) =>
      surah.name.contains(_searchQuery) ||
      surah.englishName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
      surah.englishNameTranslation.toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }
}
