import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../providers/quran_provider.dart'; // افترض وجود هذا الملف
import '../../models/quran_models.dart'; // افترض وجود هذا الملف
import 'surah_reading_screen.dart'; // افترض وجود هذا الملف
import '../../utils/rtl_helper.dart'; // افترض وجود هذا الملف
import '../../providers/settings_provider.dart'; // لضبط حجم الخط في النتائج

class QuranSearchScreen extends StatefulWidget {
  const QuranSearchScreen({super.key});

  @override
  State<QuranSearchScreen> createState() => _QuranSearchScreenState();
}

class _QuranSearchScreenState extends State<QuranSearchScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  List<Surah> _surahResults = [];
  List<Ayah> _ayahResults = [];
  List<Map<String, dynamic>> _onlineResults = []; // نتائج البحث عبر الإنترنت

  bool _isSearching = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    // لتجنب البحث المتكرر بنفس النص عند كل تغيير بسيط (مثل focus)
    if (query != _currentQuery) {
      _currentQuery = query;
      // تأخير بسيط قبل إجراء البحث لتقليل الضغط عند الكتابة السريعة
      Future.delayed(const Duration(milliseconds: 300), () {
        if (query == _searchController.text.trim()) { // تأكد أن النص لم يتغير مرة أخرى
          _performSearch(query);
        }
      });
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _surahResults.clear();
        _ayahResults.clear();
        _onlineResults.clear();
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    try {
      // البحث المحلي عن السور
      final surahResults = quranProvider.searchSurahs(query);

      // البحث المحلي عن الآيات
      final ayahResults = quranProvider.searchAyahs(query);

      // البحث عبر الإنترنت إذا كان النص معتبراً
      List<Map<String, dynamic>> onlineResults = [];
      if (query.length >= 3) { // مثال: البحث عبر الإنترنت يتطلب 3 أحرف على الأقل
        onlineResults = await quranProvider.searchVersesOnline(query);
      }

      if (mounted) {
        setState(() {
          _surahResults = surahResults;
          _ayahResults = ayahResults;
          _onlineResults = onlineResults;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في البحث: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settings = Provider.of<SettingsProvider>(context);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في القرآن الكريم'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 1,
        // شريط البحث مدمج في AppBar
        flexibleSpace: SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(top: kToolbarHeight + 8, left: 16, right: 16, bottom: 8),
            child: TextField(
              controller: _searchController,
              style: TextStyle(color: theme.colorScheme.onSurface, fontSize: settings.fontSize),
              decoration: InputDecoration(
                hintText: 'ابحث في السور، الآيات...',
                hintStyle: TextStyle(color: theme.hintColor.withOpacity(0.8), fontSize: settings.fontSize -1),
                prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: theme.colorScheme.primary),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch(''); // مسح النتائج عند مسح النص
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: theme.cardColor, // لون خلفية شريط البحث
                contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
              ),
              textDirection: TextDirection.rtl,
              autofocus: true,
              onSubmitted: _performSearch, // إجراء البحث عند الضغط على "تم"
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withOpacity(0.7),
          indicatorColor: Colors.white, // لون مؤشر التبويب
          indicatorWeight: 2.5,
          labelStyle: TextStyle(fontSize: settings.fontSize -1, fontWeight: FontWeight.w600),
          tabs: [
            Tab(text: 'السور (${_surahResults.length})'),
            Tab(text: 'الآيات (${_ayahResults.length})'),
            Tab(text: 'متقدم (${_onlineResults.length})'),
          ],
        ),
      ),
      body: _isSearching
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSurahResults(settings),
                _buildAyahResults(settings),
                _buildOnlineResults(settings),
              ],
            ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon, SettingsProvider settings) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Theme.of(context).hintColor.withOpacity(0.5)),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(fontSize: settings.fontSize, color: Theme.of(context).hintColor),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSurahResults(SettingsProvider settings) {
    if (_currentQuery.isEmpty && _surahResults.isEmpty) {
      return _buildEmptyState('ابدأ بكتابة اسم السورة للبحث', Icons.search_rounded, settings);
    }
    if (_surahResults.isEmpty) {
      return _buildEmptyState('لم يتم العثور على سور تطابق بحثك', Icons.search_off_rounded, settings);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: _surahResults.length,
      itemBuilder: (context, index) {
        final surah = _surahResults[index];
        final theme = Theme.of(context);
        return Card(
          margin: const EdgeInsets.only(bottom: 10),
          elevation: 1.5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            leading: Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [theme.colorScheme.primary, theme.colorScheme.primary.withOpacity(0.7)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '${surah.number}',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: settings.fontSize -2),
                ),
              ),
            ),
            title: RTLHelper.buildDirectionalText(
              surah.name,
              style: TextStyle(fontSize: settings.fontSize + 2, fontWeight: FontWeight.w600, color: theme.textTheme.bodyLarge?.color),
              forceRTL: true,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(surah.englishNameTranslation, style: TextStyle(fontSize: settings.fontSize - 2, color: theme.hintColor)),
                const SizedBox(height: 2),
                Text(
                  '${surah.numberOfAyahs} آية • ${surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية'}',
                  style: TextStyle(color: theme.hintColor.withOpacity(0.8), fontSize: settings.fontSize - 3),
                ),
              ],
            ),
            trailing: Icon(Icons.arrow_forward_ios_rounded, color: theme.hintColor.withOpacity(0.7), size: 18),
            onTap: () {
              final quranProvider = Provider.of<QuranProvider>(context, listen: false);
              // افترض أن loadSurah يعيد السورة كاملة
              quranProvider.loadSurah(surah.number).then((fullSurah) {
                if (fullSurah != null) {
                   quranProvider.setLastReadSurah(fullSurah.number, null);
                   Navigator.push(
                     context,
                     MaterialPageRoute(builder: (context) => SurahReadingScreen(surah: fullSurah)),
                   );
                } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('لم يتم العثور على تفاصيل السورة')));
                }
              });
            },
          ),
        );
      },
    );
  }

  Widget _buildAyahResults(SettingsProvider settings) {
    if (_currentQuery.isEmpty && _ayahResults.isEmpty) {
      return _buildEmptyState('ابدأ بكتابة نص الآية للبحث', Icons.document_scanner_outlined, settings);
    }
    if (_ayahResults.isEmpty) {
      return _buildEmptyState('لم يتم العثور على آيات تطابق بحثك', Icons.search_off_rounded, settings);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: _ayahResults.length,
      itemBuilder: (context, index) {
        final ayah = _ayahResults[index];
        // محاولة جلب اسم السورة من خلال الآية نفسها أو من QuranProvider
        // هذا يعتمد على كيفية بناء نموذج Ayah لديك
        final Surah? surah = Provider.of<QuranProvider>(context, listen: false)
            .getSurahOfAyah(ayah.number); // افترض وجود هذه الدالة
        final theme = Theme.of(context);

        return Card(
          margin: const EdgeInsets.only(bottom: 10),
          elevation: 1.5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${surah?.name ?? "سورة"} - آية ${ayah.numberInSurah}',
                        style: TextStyle(color: theme.colorScheme.primary, fontWeight: FontWeight.w600, fontSize: settings.fontSize - 3),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'جزء ${ayah.juz} | صفحة ${ayah.page}',
                      style: TextStyle(color: theme.hintColor, fontSize: settings.fontSize - 3),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  ayah.text,
                  style: TextStyle(fontSize: settings.fontSize + settings.arabicFontSizeFactor, height: 1.9, fontFamily: settings.quranFontFamily),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.justify,
                ),
                const SizedBox(height: 10),
                // أزرار الإجراءات (تشغيل، مفضلة، مشاركة)
                _buildAyahActionButtons(ayah, surah, settings),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOnlineResults(SettingsProvider settings) {
     if (_currentQuery.isEmpty || _currentQuery.length < 3) {
      return _buildEmptyState('اكتب 3 أحرف على الأقل للبحث المتقدم', Icons.cloud_search_outlined, settings);
    }
    if (_onlineResults.isEmpty) {
      return _buildEmptyState('لا توجد نتائج في البحث المتقدم', Icons.cloud_off_rounded, settings);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: _onlineResults.length,
      itemBuilder: (context, index) {
        final result = _onlineResults[index];
        final theme = Theme.of(context);
        return Card(
          margin: const EdgeInsets.only(bottom: 10),
          elevation: 1.5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${result['surah_name'] ?? 'سورة غير محددة'} - آية ${result['verse_number'] ?? ''}',
                    style: TextStyle(color: theme.colorScheme.secondary, fontWeight: FontWeight.w600, fontSize: settings.fontSize - 3),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  result['text'] ?? 'لا يوجد نص',
                  style: TextStyle(fontSize: settings.fontSize + settings.arabicFontSizeFactor, height: 1.9, fontFamily: settings.quranFontFamily),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.justify,
                ),
                if (result['translation'] != null && result['translation'].isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    "الترجمة: ${result['translation']}",
                    style: TextStyle(fontSize: settings.fontSize - 2, color: theme.hintColor, fontStyle: FontStyle.italic),
                  ),
                ],
                 // يمكن إضافة أزرار إجراءات هنا إذا كانت البيانات تسمح (مثل الانتقال للآية)
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAyahActionButtons(Ayah ayah, Surah? surah, SettingsProvider settings) {
    if (surah == null) return const SizedBox.shrink(); // لا تعرض الأزرار إذا لم يتم تحديد السورة
    final theme = Theme.of(context);
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);

    return Row(
      mainAxisAlignment: MainAxisAlignment.end, // محاذاة الأزرار لليمين
      children: [
        IconButton(
          icon: Icon(Icons.play_circle_outline_rounded, color: theme.colorScheme.primary, size: 26),
          tooltip: "تشغيل",
          onPressed: () => quranProvider.playAyah(surah.number, ayah.numberInSurah),
        ),
        FutureBuilder<bool>(
          future: quranProvider.isBookmarked(surah.number, ayah.numberInSurah),
          builder: (context, snapshot) {
            final isBookmarked = snapshot.data ?? false;
            return IconButton(
              icon: Icon(
                isBookmarked ? Icons.bookmark_rounded : Icons.bookmark_border_rounded,
                color: isBookmarked ? theme.colorScheme.secondary : theme.hintColor,
                size: 26,
              ),
              tooltip: isBookmarked ? "إزالة من المفضلة" : "إضافة للمفضلة",
              onPressed: () async {
                if (isBookmarked) {
                  await quranProvider.removeBookmark(surah.number, ayah.numberInSurah);
                   if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت إزالة العلامة')));
                } else {
                  await quranProvider.addBookmark(surah.number, ayah.numberInSurah);
                  if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت إضافة العلامة')));
                }
                setState(() {}); // لتحديث أيقونة المفضلة مباشرة
              },
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.share_outlined, color: theme.hintColor, size: 24),
          tooltip: "مشاركة",
          onPressed: () {
            final shareText = '''"${ayah.text}"
(الآية ${ayah.numberInSurah} من سورة ${surah.name})
- من تطبيق قرآني''';
            Share.share(shareText);
          },
        ),
        // زر للانتقال إلى الآية في شاشة القراءة
        IconButton(
          icon: Icon(Icons.open_in_new_rounded, color: theme.hintColor, size: 24),
          tooltip: "فتح في المصحف",
          onPressed: () {
             quranProvider.loadSurah(surah.number).then((fullSurah) {
                if (fullSurah != null) {
                   quranProvider.setLastReadSurah(fullSurah.number, ayah.numberInSurah);
                   Navigator.push(
                     context,
                     MaterialPageRoute(builder: (context) => SurahReadingScreen(surah: fullSurah, initialAyahNumber: ayah.numberInSurah)),
                   );
                }
              });
          },
        ),
      ],
    );
  }
}
```

**التغييرات الرئيسية:**

1.  **تصميم شريط AppBar:**
    * تم دمج حقل البحث مباشرة في `flexibleSpace` الخاص بـ `AppBar` ليكون أكثر بروزًا وحداثة.
    * تم تحديث ألوان `TabBar` لتتناسب مع لون `AppBar`.
2.  **تصميم حقل البحث:**
    * استخدام `cardColor` للخلفية وزوايا دائرية أكثر.
    * أيقونات واضحة للبحث والمسح.
3.  **عرض النتائج (`_buildSurahResults`, `_buildAyahResults`, `_buildOnlineResults`):**
    * استخدام `Card` بتصميم محدث (elevation, shape, margin).
    * تحسين `ListTile` لعرض معلومات السورة والآية بشكل أوضح وأجمل.
    * استخدام `RTLHelper` لضمان محاذاة النصوص بشكل صحيح للغة العربية.
    * استخدام حجم الخط من `SettingsProvider` لعرض النصوص.
4.  **الحالات الفارغة (`_buildEmptyState`):**
    * دالة موحدة لعرض حالات عدم وجود نتائج أو الحاجة لبدء البحث، مع أيقونات معبرة.
5.  **أزرار الإجراءات للآيات (`_buildAyahActionButtons`):**
    * تم تجميعها في دالة منفصلة.
    * أيقونات محدثة وأكثر وضوحًا.
    * إضافة زر "فتح في المصحف" للانتقال المباشر للآية في شاشة القراءة.
6.  **تحسينات طفيفة في UX:**
    * تأخير بسيط قبل إجراء البحث لتقليل الاستدعاءات المتكررة عند الكتابة السريعة.
    * إجراء البحث عند الضغط على زر "تم" في لوحة المفاتيح (`onSubmitted`).
    * مسح النتائج عند مسح نص البحث.

**ملاحظات هامة:**

* **`QuranProvider`**: هذا الملف يعتمد بشكل كبير على وظائف موجودة في `QuranProvider` مثل `searchSurahs`, `searchAyahs`, `searchVersesOnline`, `getSurahOfAyah`, `loadSurah`, `isBookmarked`, `addBookmark`, `removeBookmark`, `playAyah`. تأكد من أن هذه الدوال موجودة وتعمل بشكل صحيح.
* **`SettingsProvider`**: يستخدم لجلب إعدادات الخط (`fontSize`, `arabicFontSizeFactor`, `quranFontFamily`).
* **الانتقال إلى القراءة**: عند الضغط على نتيجة بحث (سورة أو آية)، يتم الانتقال إلى `SurahReadingScreen`. تأكد من أن هذه الشاشة تستقبل البيانات بشكل صحيح ويمكنها عرض السورة أو تحديد الآية المطلوبة.

بعد هذه التعديلات، يجب أن تبدو شاشة البحث أكثر حداثة وتوفر تجربة استخدام أفضل.

هل ترغب الآن في مراجعة أو تعديل ملف `reciter_selection_screen.dar